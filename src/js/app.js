(function ($) {
  // --- GLOBAL VARIABLES --- //
  var $window = $(window);
  var $document = $(document);
  var $header = $(".header");
  var bpXSmall = 575;
  var bpSmall = 771;
  var bpMedium = 992;
  var bpLarge = 1200;
  var classIsActive = "is-active";
  var classIsDisabled = "is-disabled";
  var classIsLast = "is-last";
  var classHasError = "has-error";
  var classIsLoading = "is-loading";
  var classIsOpened = "is-opened";
  var classIsSelected = "is-selected";
  var classIsCollapse = "is-collapse";
  var classIsAnimated = "is-animated";
  var $this = null;
  var $containers = $("html,body");

  // --- COMPONENTS, PARTS --- //
  $(document).ready(function () {
    // ---  COMPONENTS --- //
    // Modal
    componentModal();
    // Select
    componentSelect();
    // Masonry
    componentWorksMasonry();
    // Swiper
    componentSwiper();
    // Gallery
    componentGallery();
    // Navigation
    componentFullscreenNavigation();
    // Ajax
    componentAjaxLoad();
    // Side navigation
    componentSideNavigation();

    // selectTag
    componentSelectTag();

    // Clear Filter
    componentClearFilter();

    // Scroll to
    partScrollTo();
    // Show Hidden
    partShowHidden();
    // Scroll Resize
    partScrollResize();

    // Disable hidden inputs on form submit
    $("#form-filter").on("submit", function () {
      $(this).find("input:hidden, select:hidden").prop("disabled", true);
    });
  });

  var resizeTimer;

  // --- SCROLL EVENT --- //
  $(document).on("scroll", function () {
    if (resizeTimer) {
      window.cancelAnimationFrame(resizeTimer);
    }

    resizeTimer = window.requestAnimationFrame(function () {
      partScrollResize();
      componentSideNavigation();
    });
  });

  // --- RESIZE EVENT --- //
  $(window).on("resize", function () {
    if (resizeTimer) {
      window.cancelAnimationFrame(resizeTimer);
    }

    resizeTimer = window.requestAnimationFrame(function () {
      partScrollResize();
    });
  });

  // --- LOAD EVENT --- //
  $(function () {
    // Page Loader
    $("body").addClass(classIsActive);
  });

  //====================================================
  //  Function: Load more content
  //====================================================
  function componentAjaxLoad() {
    if ($(".js-load-more-trigger").length) {
      $(".js-load-more-trigger").on("click", function () {
        var loadMoreBlock = $(this).closest(".js-load-more-block");
        $.ajax({
          url: scd_ajax.url,
          data: {
            action: "loadmore",
            query: scd_ajax.query_vars,
            page: scd_ajax.current_page,
          },
          type: "POST",
          beforeSend: function () {
            loadMoreBlock.find(".js-load-more").addClass(classIsDisabled);
            loadMoreBlock
              .find(".js-load-more .js-load-more-spinner")
              .addClass(classIsActive);
          },
          success: function (data) {
            setTimeout(function () {
              loadMoreBlock.find(".js-load-more-grid").append(data.html);

              loadMoreBlock.find(".js-load-more").removeClass(classIsDisabled);
              loadMoreBlock
                .find(".js-load-more .js-load-more-spinner")
                .removeClass(classIsActive);

              // partDots()
              // if ($('.js-gallery').length) {
              //   // gallery.destroy(true);
              //   componentGallery()
              // }

              loadMoreBlock.addClass(classIsLast);

              scd_ajax.current_page++;
              if (data.max_num_pages <= scd_ajax.current_page) {
                loadMoreBlock.find(".js-load-more").addClass("d-none");
              }

              $(document.body).trigger("post-load");
            }, 500);
          },
          error: function () {
            console.log("error");
          },
        });
      });
    }
  }

  // .js-load-more-block
  // 		.js-load-more-grid
  // 		.js-load-more.load-more
  //			.load-more__button.js-load-more-trigger(data-content="portfolio") Další reference
  //			.load-more__spinner.loading-spinner.js-load-more-spinner

  //====================================================
  //  Function: Clear filter
  //====================================================
  function componentClearFilter() {
    if ($(".js-clear-filter-trigger").length) {
      // Clear selected checkbox
      $(document).on("click", ".js-clear-filter-trigger", function (e) {
        var inst = $(this);
        var clearBlock = inst.closest(".js-clear-filter-block");

        // Reset all input elements to their default values
        clearBlock.find("input").val("");
        clearBlock.find(".js-checkbox-input-hidden").prop("checked", false).trigger("change");
        clearBlock.removeClass("is-selected");

        // Reset select elements to their default values
        clearBlock.find("select").each(function () {
          var $select = $(this);
          var selectId = $select.attr("id");
          var defaultValue = "0"; // Default for most selects (vyrobca, dizajner, etc.)

          // Special cases: rocnik and kolekcia use empty string as default
          if (selectId === "rocnik" || selectId === "kolekcia") {
            defaultValue = "";
          }

          // For Select2 elements, reset and trigger change
          if ($select.hasClass("js-select2")) {
            $select.val(defaultValue).trigger("change.select2");
          } else {
            $select.val(defaultValue).trigger("change");
          }
        });

        // Reset tag selections
        $(".js-select-tag-block .js-select-tag-trigger").removeClass(
          "is-selected",
        );
        $(".js-select-tag-block .js-select-tag-trigger")
          .first()
          .addClass("is-selected");

        // Reset custom multiselect component visual state
        // This ensures the custom multiselect UI is synchronized with the reset checkboxes
        if (window.resetCustomMultiselect && typeof window.resetCustomMultiselect === 'function') {
          window.resetCustomMultiselect();
        }

        // Clear all query variables to reset filters to default state
        var defaultQueryVars = {};
        // Preserve essential variables that shouldn't be cleared
        if (scd_ajax.query_vars.lang) {
          defaultQueryVars.lang = scd_ajax.query_vars.lang;
        }
        if (
          scd_ajax.query_vars.post_type &&
          Array.isArray(scd_ajax.query_vars.post_type)
        ) {
          defaultQueryVars.post_type = scd_ajax.query_vars.post_type;
        }
        scd_ajax.query_vars = defaultQueryVars;
        scd_ajax.current_page = 0;

        // Trigger filtering to refresh results with cleared filters
        var loadMoreBlock = $(".js-load-more-block");
        var contentBlock = $(".js-load-more-grid");

        $.ajax({
          url: scd_ajax.url,
          data: {
            action: "loadmore",
            query: scd_ajax.query_vars,
            page: scd_ajax.current_page,
          },
          type: "POST",
          beforeSend: function () {
            loadMoreBlock.find(".js-load-more").addClass(classIsDisabled);
            loadMoreBlock
              .find(".js-load-more .js-load-more-spinner")
              .addClass(classIsActive);
          },
          success: function (data) {
            setTimeout(function () {
              contentBlock.html(data.html);

              loadMoreBlock.find(".js-load-more").removeClass(classIsDisabled);
              loadMoreBlock
                .find(".js-load-more .js-load-more-spinner")
                .removeClass(classIsActive);

              loadMoreBlock.addClass(classIsLast);

              scd_ajax.current_page++;
              if (data.max_num_pages <= scd_ajax.current_page) {
                loadMoreBlock.find(".js-load-more").addClass("d-none");
              } else {
                loadMoreBlock.find(".js-load-more").removeClass("d-none");
              }

              $(document.body).trigger("post-load");

              // Update URL to reflect cleared filters
              var url_qvs = {};
              const ignore_qvs = ["s", "lang", "paged", "category__not_in"];

              for (const prop in scd_ajax.query_vars) {
                if (prop === "post_type") {
                  if (
                    !scd_ajax.post_type_filter ||
                    scd_ajax.query_vars[prop] instanceof Array ||
                    !scd_ajax.query_vars[prop]
                  ) {
                    continue;
                  }
                }
                if (
                  prop === "edesignum" ||
                  prop === "designum" ||
                  (scd_ajax.query_vars[prop] !== "" &&
                    !ignore_qvs.includes(prop))
                ) {
                  url_qvs[prop] = scd_ajax.query_vars[prop];
                }
              }

              history.replaceState(
                {},
                "",
                scd_ajax.current_url +
                  (Object.keys(url_qvs).length
                    ? "?" + jQuery.param(url_qvs)
                    : ""),
              );

              $("html, body")
                .stop()
                .animate({
                  scrollTop:
                    $("section.primary").offset().top -
                    (windowWidth > bpMedium ? 125 + 5 : 10),
                });

              afterDesignumFilter();
            }, 50);
          },
          error: function () {
            console.log("error");
          },
        });

        e.preventDefault();
      });

      $(".checkbox__input-hidden").on("change", function () {
        checkIfFilterIsSelected();
      });
    }
  }

  function checkIfFilterIsSelected() {
    var allChecked = false;
    var allSelected = false;
    $(".js-clear-filter-block .checkbox__input-hidden").each(function () {
      if ($(this).prop("checked") == true) {
        allChecked = true;
      }
    });

    $(".js-clear-filter-block select").each(function () {
      if ($(this).val() !== "all") {
        allSelected = true;
      }
    });

    if (allChecked || allSelected) {
      $(".js-clear-filter-block").addClass("is-selected");
    } else {
      $(".js-clear-filter-block").removeClass("is-selected");
    }
  }

  //====================================================
  //  Function: Fullscreen navigation
  //====================================================
  function componentFullscreenNavigation() {
    if ($(".js-navigation").length) {
      $(".js-navigation-trigger").on("click", function (e) {
        if ($(".js-navigation.is-collapse").length) {
          $(".search__icon.js-active-class-toggle").trigger("click");
        }
        $(this).closest(".js-navigation").toggleClass(classIsCollapse);
        $("body").toggleClass("overflow-hidden");
        e.preventDefault();
      });
      checkNavigationTrigger();
    }
  }

  function checkNavigationTrigger() {
    (function ($) {
      if (
        $(".js-navigation").hasClass(classIsCollapse) &&
        $(window).width() > bp_medium
      ) {
        $(".js-navigation").removeClass(classIsCollapse);
        $("body").removeClass("overflow-hidden");
        // $("main, footer").show();
      }
    })(jQuery);
  }

  //====================================================
  //  Function: Gallery
  //====================================================
  var gallery;

  function componentGallery() {
    if ($(".js-open-gallery").length) {
      $(document).on("click", ".js-open-gallery", function () {
        var inst = $(this);
        setTimeout(() => {
          $("body").addClass("is-modal-open");
          $(".js-modal-overlay").addClass(classIsActive);
          $('.js-modal[data-modal="modal-gallery"]').addClass(classIsActive);

          createModalGallerySlider(inst);
        }, 10);
        return false;
      });
    }

    $(".js-open-gallery-item").on("click", function () {
      $(this)
        .closest(".js-gallery-block")
        .find(".js-open-gallery")
        .trigger("click");
      setTimeout(() => {
        var itemId = $(this).closest(".js-gallery-item").index();
        galleryModalInst.slideToLoop(itemId, 0);
      }, 50);
      return false;
    });

    $(".js-open-gallery-item-first").on("click", function () {
      $(this)
        .closest(".js-gallery-block")
        .find(".js-open-gallery")
        .trigger("click");
      return false;
    });
  }

  function createModalGallerySlider(gallery) {
    var galleryData = gallery.data("gallery");
    var galleryItemTemplate;
    galleryModalInst.removeAllSlides();

    for (var i = 0; i < galleryData.length; i++) {
      galleryItemTemplate = `
    <div class="swiper-slide">
      <img src="${galleryData[i].src}" alt="" class="margin-center img--responsive img--contain" loading="lazy">
		<p class="modal-gallery__text">${galleryData[i].description}</p>
    </div>`;
      galleryModalInst.appendSlide(galleryItemTemplate);
    }

    if (galleryData.length === 1) {
      galleryModalInst.allowSlidePrev = galleryModalInst.allowSlideNext = false;
      galleryArrowGroup.hide();
    } else {
      galleryModalInst.allowSlidePrev = galleryModalInst.allowSlideNext = true;
      galleryArrowGroup.show();
    }

    galleryModalInst.slideToLoop(0, 0);
  }

  //====================================================
  //  Function: Masonry
  //====================================================
  var worksMasonry;

  function componentWorksMasonry() {
    if ($(".js-works-masonry").length && $(window).width() > bpXSmall) {
      worksMasonry = $(".js-works-masonry").colcade({
        columns: ".masonry__column",
        items: ".js-masonry__item",
      });
    }
  }

  //====================================================
  //  Function: Modal
  //====================================================
  function componentModal() {
    // open modal
    $(".js-modal-trigger").on("click", function () {
      var inst = $(this);
      var modalId = inst.data("modal");

      if ($(".js-modal.is-active").length) {
        $(".js-modal." + classIsActive + "").addClass("modal-hidden");
      } else {
        $(".js-modal." + classIsActive + "").removeClass(classIsActive);
        $(".js-modal-overlay").addClass(classIsActive);
      }
      $(".js-modal[data-modal=" + modalId + "]").addClass(classIsActive);

      $("body").addClass("is-modal-open");
      return false;
    });

    // close modal
    $(".js-close-modal").on("click", function () {
      if ($(".modal-hidden").length) {
        $(".modal-hidden").removeClass("modal-hidden");
      } else {
        $(".js-modal-overlay").removeClass(classIsActive);
      }
      $(this)
        .closest(".js-modal." + classIsActive + "")
        .removeClass(classIsActive);
      $("body").removeClass("is-modal-open");
      return false;
    });

    // close modal keypress key escape
    $(document).on("keydown", function (event) {
      if (event.keyCode == 27 && $(".js-modal").hasClass(classIsActive)) {
        $(".js-close-modal").trigger("click");
      }
    });

    // close modal on click outside from modal box
    $(document).on("click", function (event) {
      if (!$(event.target).closest(".js-modal-body").length) {
        $(".js-close-modal").trigger("click");
      }
    });
  }

  //====================================================
  //  Function: Select tag
  //====================================================
  function componentSelectTag() {
    $(".js-select-tag-trigger").on("click", AJAXFilter);
    $(".js-select, .js-checkbox").on("change", AJAXFilter);
    $(".js-select2").on("select2:select", AJAXFilter);
  }

  function AJAXFilter(e) {
    var loadMoreBlock = $(".js-load-more-block");
    var contentBlock = $(".js-load-more-grid");
    var $this = $(this);
    var qv = $this.closest("[data-qvar]").data("qvar");
    if ($this.is('input[type="checkbox"]')) {
      if ($this.is(".switch")) {
        if ($this.is(":checked")) {
          scd_ajax.query_vars[qv] = "";
        } else {
          delete scd_ajax.query_vars[qv];
        }
      } else {
        if (typeof scd_ajax.query_vars[qv] == "undefined") {
          scd_ajax.query_vars[qv] = [];
        } else if (typeof scd_ajax.query_vars[qv] == "string") {
          scd_ajax.query_vars[qv] = [scd_ajax.query_vars[qv]];
        }
        if ($this.is(":checked")) {
          scd_ajax.query_vars[qv].push($this.val());
        } else {
          scd_ajax.query_vars[qv] = scd_ajax.query_vars[qv].filter(
            function (item) {
              return item !== $this.val();
            },
          );
        }
      }
    } else {
      if (qv) {
        scd_ajax.query_vars[qv] = $this.data("id") || $this.val();
      }
    }

    var mutex = $this.closest("[data-mutex]").data("mutex");
    if (mutex) {
      for (var i = 0; i < mutex.length; i++) {
        if (mutex[i] !== qv) {
          delete scd_ajax.query_vars[mutex[i]];
        }
        $('[data-qvar="' + mutex[i] + '"]')
          .not($this)
          .val("")
          .prop("checked", false)
          .trigger("change.select2")
          .closest(".js-select-tag-block")
          .find(".js-select-tag-trigger")
          .removeClass("is-selected");
      }
    }

    $.ajax({
      url: scd_ajax.url,
      data: {
        action: "loadmore",
        query: scd_ajax.query_vars,
        page: 0, // Always start from page 0 for filter requests (becomes page 1 in PHP)
      },
      type: "POST",
      beforeSend: function () {
        loadMoreBlock.find(".js-load-more").addClass(classIsDisabled);
        loadMoreBlock
          .find(".js-load-more .js-load-more-spinner")
          .addClass(classIsActive);
      },
      success: function (data) {
        setTimeout(function () {
          contentBlock.html(data.html);

          loadMoreBlock.find(".js-load-more").removeClass(classIsDisabled);
          loadMoreBlock
            .find(".js-load-more .js-load-more-spinner")
            .removeClass(classIsActive);

          loadMoreBlock.addClass(classIsLast);

          $this
            .closest(".js-select-tag-block")
            .find(".js-select-tag-trigger")
            .removeClass("is-selected");
          $this.toggleClass("is-selected");

          // Reset current_page to 1 after filter is applied (since we loaded page 1)
          scd_ajax.current_page = 1;
          // Update max_num_pages from the response
          scd_ajax.max_num_pages = data.max_num_pages;

          if (data.max_num_pages <= scd_ajax.current_page) {
            loadMoreBlock.find(".js-load-more").addClass("d-none");
          } else {
            loadMoreBlock.find(".js-load-more").removeClass("d-none");
          }

          $(document.body).trigger("post-load");

          var url_qvs = {};
          const ignore_qvs = ["s", "lang", "paged", "category__not_in"];

          for (const prop in scd_ajax.query_vars) {
            if (prop === "post_type") {
              if (
                !scd_ajax.post_type_filter ||
                scd_ajax.query_vars[prop] instanceof Array ||
                !scd_ajax.query_vars[prop]
              ) {
                continue;
              }
            }
            if (
              prop === "edesignum" ||
              prop === "designum" ||
              (scd_ajax.query_vars[prop] !== "" && !ignore_qvs.includes(prop))
            ) {
              url_qvs[prop] = scd_ajax.query_vars[prop];
            }
          }

          history.replaceState(
            {},
            "",
            scd_ajax.current_url + "?" + jQuery.param(url_qvs),
          );

          $("html, body")
            .stop()
            .animate({
              scrollTop:
                $("section.primary").offset().top -
                (windowWidth > bpMedium ? 125 + 5 : 10),
            });

          afterDesignumFilter();
        }, 50);
      },
      error: function () {
        console.log("error");
      },
    });

    if (!$this.is('input[type="checkbox"]')) {
      e.preventDefault();
      return false;
    }
  }

  //====================================================
  //  Function: Select
  //====================================================
  function componentSelect() {
    if ($(".js-select").length) {
      $(".js-select")
        .select2({
          minimumResultsForSearch: Infinity,
          placeholder: function () {
            $(this).data("placeholder");
          },
        })
        .on("focus", function () {
          $(this).select2("open");
        });

      selectEvents($(".js-select"));
    }

    if ($(".js-select-object").length) {
      $(".js-select-object")
        .select2({
          minimumResultsForSearch: Infinity,
          templateResult: typeOfColor,
          dropdownCssClass: "select-medium-dropdown",
          placeholder: function () {
            $(this).data("placeholder");
          },
        })
        .on("focus", function () {
          $(this).select2("open");
        });

      selectEvents($(".js-select-object"));
    }
  }

  function typeOfColor(state) {
    if (!state.id) {
      return state.text;
    }
    // console.log(state);
    var optionColor = state.element.dataset.color;
    var optionStatus = state.element.dataset.status;
    var optionStatusText = state.element.dataset.statusText;
    var $state = $(
      '<span class="select-option"><span class="select-option-left-panel"><span class="select-option-color circle circle-25 bg-' +
        optionColor +
        '-normal"></span><span class="select-option-text">' +
        state.text +
        '</span></span><span class="select-option-right-panel"><span class="select-option-status ' +
        optionStatus +
        '">' +
        optionStatusText +
        "</span></span></span>",
    );
    return $state;
  }

  function selectEvents(element) {
    element.on("select2:open", function (e) {
      $(this).closest(".js-placeholder-block").addClass(classIsActive);
    });

    element.on("select2:select", function (e) {
      $(this).closest(".js-placeholder-block").addClass(classIsActive);
      $(this).trigger("input");
      checkIfFilterIsSelected();
    });

    element.on("select2:close", function (e) {
      if (!$(this).val().length) {
        $(this).closest(".js-placeholder-block").removeClass(classIsActive);
      }
    });
  }

  //====================================================
  //  Initialization scroll
  //====================================================
  function componentSideNavigation() {
    if ($("#scroll-menu").length) {
      checkScroll();
    }
  }

  if ($("#scroll-menu").length) {
    var scrolling = false;
    var header = $(".js-navigation");
    var contentSections = $(".section-scroll"),
      verticalNavigation = $("#scroll-menu"),
      navigationItems = verticalNavigation.find("a");
  }

  function checkScroll() {
    if (!scrolling) {
      scrolling = true;
      !window.requestAnimationFrame
        ? setTimeout(updateSections, 300)
        : window.requestAnimationFrame(updateSections);
    }
  }

  function updateSections() {
    windowScrollTop = $(window).scrollTop();

    contentSections.each(function () {
      var section = $(this),
        height = header.height() + 65,
        sectionId = section.attr("id");
      var navigationItem = navigationItems.filter(
        '[href^="#' + sectionId + '"]',
      );

      if (
        section.position().top - height < windowScrollTop &&
        section.position().top + section.innerHeight() - height >
          windowScrollTop
      ) {
        navigationItem.closest("li").addClass(classIsActive);
      } else {
        navigationItem.closest("li").removeClass(classIsActive);
      }
    });
    scrolling = false;
  }

  //====================================================
  //  Function: Slider
  //====================================================
  function componentSwiper() {
    classicSlider();
    heroThreeColumnsSlider();
    peopleSlider();
    worksSlider();
    gallerySlider();
  }

  function classicSlider() {
    if ($(".js-slider-classic").length) {
      $(".js-slider-classic").each(function () {
        var inst = $(this),
          swiperBlock = inst.closest(".js-slider-group"),
          // swiperPagination = swiperBlock.find('.js-swiper-pagination'),
          swiperArrowPrev = swiperBlock.find(".js-swiper-arrow-prev"),
          swiperArrowNext = swiperBlock.find(".js-swiper-arrow-next");

        // Convert jQuery object to DOM element for Swiper
        var swiperElement = inst[0];

        var mySwiper = new Swiper(swiperElement, {
          slidesPerView: 1,
          spaceBetween: 0,
          lazy: true,
          loop: true,
          speed: 600,
          // autoplay: {
          //   delay: 4000,
          //   disableOnInteraction: true,
          // },
          // pagination: {
          //   el: swiperPagination,
          //   clickable: true,
          // },
          keyboard: {
            enabled: true,
            onlyInViewport: false,
          },
          navigation: {
            nextEl: swiperArrowNext[0], // Convert to DOM element
            prevEl: swiperArrowPrev[0], // Convert to DOM element
          },
        });
      });
    }
  }

  function heroThreeColumnsSlider() {
    if ($(".js-slider-hero-three-columns").length) {
      $(".js-slider-hero-three-columns").each(function () {
        var inst = $(this),
          swiperBlock = inst.closest(".js-slider-group"),
          // swiperPagination = swiperBlock.find('.js-swiper-pagination'),
          swiperArrowPrev = swiperBlock.find(".js-swiper-arrow-prev"),
          swiperArrowNext = swiperBlock.find(".js-swiper-arrow-next");

        // Convert jQuery object to DOM element for Swiper
        var swiperElement = inst[0];

        var mySwiper = new Swiper(swiperElement, {
          spaceBetween: 30,
          lazy: true,
          loop: true,
          speed: 600,
          // resizeObserver: true,
          // observeParents: true,
          // observeSlideChildren: true,
          // observer: true,
          // autoplay: {
          //   delay: 4000,
          //   disableOnInteraction: true,
          // },
          // pagination: {
          //   el: swiperPagination,
          //   clickable: true,
          // },
          keyboard: {
            enabled: true,
            onlyInViewport: false,
          },
          navigation: {
            nextEl: swiperArrowNext[0], // Convert to DOM element
            prevEl: swiperArrowPrev[0], // Convert to DOM element
          },
          breakpoints: {
            200: {
              slidesPerView: 1,
            },
            576: {
              slidesPerView: 1,
            },
            768: {
              slidesPerView: 1,
            },
            992: {
              slidesPerView: 1,
            },
            1200: {
              slidesPerView: 1,
            },
            1440: {
              slidesPerView: 1,
            },
            1800: {
              slidesPerView: 1,
            },
          },
        });
      });
    }
  }

  function peopleSlider() {
    if ($(".js-slider-people").length) {
      $(".js-slider-people").each(function () {
        var inst = $(this),
          swiperBlock = inst.closest(".js-slider-group"),
          // swiperPagination = swiperBlock.find('.js-swiper-pagination'),
          swiperArrowPrev = swiperBlock.find(".js-swiper-arrow-prev"),
          swiperArrowNext = swiperBlock.find(".js-swiper-arrow-next");

        // Convert jQuery object to DOM element for Swiper
        var swiperElement = inst[0];

        var mySwiper = new Swiper(swiperElement, {
          spaceBetween: 30,
          lazy: true,
          loop: false,
          speed: 600,
          keyboard: {
            enabled: true,
            onlyInViewport: false,
          },
          navigation: {
            nextEl: swiperArrowNext[0], // Convert to DOM element
            prevEl: swiperArrowPrev[0], // Convert to DOM element
          },
          breakpoints: {
            200: {
              spaceBetween: 10,
              slidesPerView: 2,
            },
            576: {
              spaceBetween: 10,
              slidesPerView: 2,
            },
            768: {
              spaceBetween: 30,
              slidesPerView: 3,
            },
            992: {
              spaceBetween: 30,
              slidesPerView: 3,
            },
            1200: {
              spaceBetween: 30,
              slidesPerView: 4,
            },
          },
        });
      });
    }
  }

  function worksSlider() {
    if ($(".js-slider-works").length) {
      $(".js-slider-works").each(function () {
        var inst = $(this),
          swiperBlock = inst.closest(".js-slider-group"),
          // swiperPagination = swiperBlock.find('.js-swiper-pagination'),
          swiperArrowPrev = swiperBlock.find(".js-swiper-arrow-prev"),
          swiperArrowNext = swiperBlock.find(".js-swiper-arrow-next");

        // Convert jQuery object to DOM element for Swiper
        var swiperElement = inst[0];

        var mySwiper = new Swiper(swiperElement, {
          spaceBetween: 30,
          lazy: true,
          loop: false,
          speed: 600,
          keyboard: {
            enabled: true,
            onlyInViewport: false,
          },
          navigation: {
            nextEl: swiperArrowNext[0], // Convert to DOM element
            prevEl: swiperArrowPrev[0], // Convert to DOM element
          },
          breakpoints: {
            200: {
              spaceBetween: 10,
              slidesPerView: 2,
            },
            576: {
              spaceBetween: 10,
              slidesPerView: 2,
            },
            768: {
              spaceBetween: 30,
              slidesPerView: 2,
            },
            992: {
              spaceBetween: 30,
              slidesPerView: 3,
            },
            1200: {
              spaceBetween: 60,
              slidesPerView: 3,
            },
          },
        });
      });
    }
  }

  var galleryModalInst;
  var galleryArrowGroup;

  function gallerySlider() {
    if ($(".js-slider-gallery").length) {
      $(".js-slider-gallery").each(function () {
        var inst = $(this),
          swiperBlock = inst.closest(".js-slider-group"),
          // swiperPagination = swiperBlock.find('.js-swiper-pagination'),
          swiperArrowPrev = swiperBlock.find(".js-swiper-arrow-prev"),
          swiperArrowNext = swiperBlock.find(".js-swiper-arrow-next");

        // Check if swiper-wrapper exists and has children before initializing
        var swiperWrapper = inst.find(".swiper-wrapper");
        if (
          swiperWrapper.length === 0 ||
          swiperWrapper.children().length === 0
        ) {
          console.warn(
            "Swiper gallery: No slides found, skipping initialization",
          );
          return; // Skip this iteration if no slides are present
        }

        // Convert jQuery object to DOM element for Swiper
        var swiperElement = inst[0];

        galleryModalInst = new Swiper(swiperElement, {
          slidesPerView: 1,
          spaceBetween: 0,
          lazy: true,
          loop: swiperWrapper.children().length > 1, // Only enable loop if more than 1 slide
          speed: 600,
          // autoplay: {
          //   delay: 4000,
          //   disableOnInteraction: true,
          // },
          // pagination: {
          //   el: swiperPagination,
          //   clickable: true,
          // },
          keyboard: {
            enabled: true,
            onlyInViewport: false,
          },
          navigation: {
            nextEl: swiperArrowNext[0], // Convert to DOM element
            prevEl: swiperArrowPrev[0], // Convert to DOM element
          },
        });
      });

      galleryArrowGroup = $(".swiper-arrow-group");
    }
  }

  //====================================================
  //  Function: Scroll & resize events
  //====================================================
  var windowScrollTop;
  var windowWidth;
  // var bottomTop;
  // var bottomHeight;

  if ($(".js-navigation").length) {
    var navigation = $(".js-navigation");
    var secondaryMenu = $("#scroll-menu");
  }

  function partScrollResize() {
    windowScrollTop = $(window).scrollTop();
    windowWidth = $(window).width();

    if ($(".js-navigation").length && $(".header-no-scrolling").length == 0) {
      if (windowScrollTop > 1 && windowWidth > bpMedium) {
        navigation.addClass("is-scrolling");
        secondaryMenu.addClass("is-scrolling");

        fn_secondary_navigation_auto_hide(windowScrollTop);
      } else {
        navigation.removeClass("is-scrolling");
        secondaryMenu.removeClass("is-scrolling");
      }
    }
  }

  var actualTop = 0;
  var scrollBottom;
  var upTotal = 0;

  function fn_secondary_navigation_auto_hide(windowScrollTop) {
    if (actualTop == 0 || actualTop == undefined) {
      actualTop = windowScrollTop;
    } else {
      if (actualTop <= windowScrollTop) {
        //Dole
        upTotal = 0;
        if ($(".secondary-block").length) {
          navigation.removeClass("navigation-scroll-top");
        }
        actualTop = windowScrollTop;
        scrollBottom = true;
      } else {
        //Hore
        upTotal += actualTop - windowScrollTop;
        if ($(".secondary-block").length) {
          navigation.addClass("navigation-scroll-top");
        }

        actualTop = windowScrollTop;
        scrollBottom = false;
      }
    }
  }

  // element to detect scroll direction of
  var el = $(".js-horizontal-scroll"),
    isScrolledOnce = false,
    lastY = el.scrollTop(),
    lastX = el.scrollLeft();

  el.on("scroll", function () {
    if (!isScrolledOnce) {
      var currY = el.scrollTop(),
        currX = el.scrollLeft(),
        x = currX > lastX ? "right" : currX === lastX ? "none" : "left";

      if (x == "right") {
        $(this).addClass("is-disabled");
        isScrolledOnce = true;
      }

      lastX = currX;
    }
  });

  //====================================================
  //  Function: Scroll to
  //====================================================
  function partScrollTo() {
    if ($(".js-scroll-trigger").length) {
      $(".js-scroll-trigger").on("click", function () {
        var inst = $(this);
        var target = $(this).attr("data-scroll");
        if (windowWidth > bpMedium) {
          var navigationHeight = 125 + 5;
        } else {
          var navigationHeight = 10;
        }
        if (target.length) {
          if ($("#" + target + "").length == 0) {
            return false;
          }
          if (
            $(".js-navigation").hasClass("is-collapse") &&
            $(window).width() < bpMedium
          ) {
            $(".js-navigation").removeClass("is-collapse");
            $("body").removeClass("overflow-hidden");
          }

          $(".js-navigation").addClass("is-scroll-to");

          $("html, body")
            .stop()
            .animate(
              {
                scrollTop: $("#" + target + "").offset().top - navigationHeight,
              },
              function () {
                setTimeout(() => {
                  $(".js-navigation").removeClass("is-scroll-to");
                }, 100);
              },
            );
          return false;
        }
      });
    }
  }

  //====================================================
  //  Function: Show/hidden
  //====================================================
  function partShowHidden() {
    if ($(".js-active-class-toggle").length) {
      $(".js-active-class-toggle").on("click", function () {
        if (
          $(this).hasClass("search__icon") &&
          $(window).width() < bpMedium &&
          $(".js-navigation.is-collapse").length == 0
        ) {
          $(".js-navigation-trigger").trigger("click");
        }
        $(this)
          .closest(".js-active-block")
          .toggleClass(classIsActive)
          .find(".js-hidden-content")
          .toggleClass(classIsActive);
      });
    }

    if ($(".js-active-content-toggle").length) {
      $(".js-active-content-toggle").on("click", function (e) {
        e.preventDefault();
        var inst = $(this);
        var contentId = inst.data("content");
        $('.js-hidden-content[data-content="' + contentId + '"]').toggleClass(
          classIsActive,
        );
      });
    }

    if ($(".js-active-class-add").length) {
      $(".js-active-class-add").on("click", function () {
        $(this)
          .closest(".js-active-block")
          .find(".js-hidden-content")
          .addClass(classIsActive);
      });
    }

    if ($(".js-active-class-remove").length) {
      $(".js-active-class-remove").on("click", function () {
        $(this)
          .closest(".js-active-block")
          .find(".js-hidden-content")
          .removeClass(classIsActive);
      });
    }

    if ($(".js-active-class-hide").length) {
      $(".js-active-class-hide").on("click", function () {
        $(this).hide();
        $(this)
          .closest(".js-active-block")
          .toggleClass(classIsActive)
          .find(".js-hidden-content")
          .addClass(classIsActive);
      });
    }

    var elementOfClicked = ".search-block";
    $(document).on("click", function (event) {
      if (!$(event.target).closest(elementOfClicked).length) {
        $(elementOfClicked + ".is-active").removeClass(classIsActive);
        $(elementOfClicked)
          .find(".js-hidden-content")
          .removeClass(classIsActive);
      }
    });
  }

  function afterDesignumFilter() {
    if ($("#designum-filter").length) {
      if ("designum" in scd_ajax.query_vars) {
        $(".main-heading").html("Články Designum");
      } else if ("edesignum" in scd_ajax.query_vars) {
        $(".main-heading").html(
          "Články e-designum<br><small>ISSN 2664-5190</small>",
        );
      } else {
        $(".main-heading").html("Články");
      }
    }
  }
})(jQuery);

document.addEventListener("DOMContentLoaded", function () {
  document.querySelectorAll(".wrap-dropdown").forEach((dropdown) => {
    const header = dropdown.querySelector(".header-dropdown");

    if (header) {
      header.addEventListener("click", () => {
        dropdown.classList.toggle("active");
      });
    }
  });
});

document.addEventListener("DOMContentLoaded", function () {
  const swiperPosts = document.querySelectorAll(".posts-swiper");

  if (!swiperPosts.length) {
    return;
  }

  swiperPosts.forEach((swiperPost) => {
    const swiperElement = swiperPost.querySelector(".swiper");
    const swiper = new Swiper(swiperElement, {
      slidesPerView: 2,
      spaceBetween: 20,
      lazy: true,
      loop: false,
      speed: 600,
      navigation: {
        nextEl: swiperPost.querySelector(".swiper-next"),
        prevEl: swiperPost.querySelector(".swiper-prev"),
      },
      breakpoints: {
        200: {
          slidesPerView: 1,
        },
        350: {
          slidesPerView: 2,
        },
      },
    });
  });
});

document.addEventListener("DOMContentLoaded", function () {
  const onlineNcd = document.querySelectorAll(".online-exhibition-ncd");

  if (!onlineNcd.length) {
    return;
  }

  onlineNcd.forEach((element) => {
    const swiperElement = element.querySelector(".swiper");

    const swiper = new Swiper(swiperElement, {
      slidesPerView: 2,
      spaceBetween: 20,
      loop: false,
      lazy: true,
      speed: 600,
      grid: {
        rows: 2,
        fill: "row",
      },
      navigation: {
        nextEl: element.querySelector(".swiper-next"),
        prevEl: element.querySelector(".swiper-prev"),
      },
    });
  });
});

document.addEventListener("DOMContentLoaded", function () {
  const videos = document.getElementById("section-video-tab");
  if (!videos) return;

  const esc =
    window.CSS && CSS.escape
      ? CSS.escape
      : (s) => s.replace(/([ #.;?%&,:{}=\\@[\]$'"])/g, "\\$1");

  const links = Array.from(videos.querySelectorAll(".tag-list__link"));
  const panels = Array.from(videos.querySelectorAll(".tab-video"));

  function activate(link) {
    links.forEach((l) => l.classList.remove("is-selected"));
    panels.forEach((p) => p.classList.add("hidden"));

    const id = link.getAttribute("href").replace(/^#/, "");
    const panel = videos.querySelector("#" + esc(id));

    link.classList.add("is-selected");
    if (panel) panel.classList.remove("hidden");

    if (id) history.replaceState(null, "", "#" + id);
  }

  videos.addEventListener("click", function (e) {
    const a = e.target.closest(".tag-list__link");
    if (!a || !videos.contains(a)) return;
    e.preventDefault();
    activate(a);
  });
});

// init
document.addEventListener("DOMContentLoaded", () => {
  const sectionVideoTab = document.querySelector("#section-video-tab");
  if (!sectionVideoTab) return;

  const swiperElement = sectionVideoTab.querySelector(".swiper");

  new Swiper(swiperElement, {
    slidesPerView: 1,
    spaceBetween: 16,
    navigation: {
      nextEl: sectionVideoTab.querySelector(".swiper-next"),
      prevEl: sectionVideoTab.querySelector(".swiper-prev"),
    },
    breakpoints: {
      640: {
        slidesPerView: 2,
      },
      1024: {
        slidesPerView: 3,
      },
    },
  });
});
